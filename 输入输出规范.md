# 光电对抗仿真系统输入输出规范

## 概述

本文档详细描述了光电对抗仿真系统HTTP API的输入输出数据规范。系统支持三种主要的光电设备仿真：
1. **光电目标数据产生** - 模拟各类光电目标设备的探测、识别和成像功能
2. **光电干扰数据产生** - 模拟各类光电干扰设备的干扰效果和性能参数
3. **光电侦查数据产生** - 模拟各类光电侦察设备的目标发现、跟踪和识别功能

## API接口信息

- **接口地址**: `POST /run_simulation`
- **内容类型**: `application/json`
- **编码格式**: `UTF-8`
- **数据限制**:
  - 最大数据生成数量: 5000条
  - 最大仿真时长: 无限制
  - 支持多线程并行处理

---

## 1. 光电目标数据产生

光电目标设备主要用于探测、识别和跟踪目标，包括红外目标、激光目标、电视目标等。

### 1.1 输入数据规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| model | string | 目标设备型号名称 | - | - | 自定义 | "多目标_红外目标A" | 用于标识设备类型和特性 |
| latitude | float | 设备纬度坐标 | 度 | -90~90 | - | 39.9042 | WGS84坐标系 |
| longitude | float | 设备经度坐标 | 度 | -180~180 | - | 116.4074 | WGS84坐标系 |
| altitude | float | 设备海拔高度 | 米 | 0~50000 | - | 1500.0 | 相对海平面高度 |
| azimuth | float | 观测方位角 | 度 | 0~360 | - | 45.0 | 正北为0度，顺时针 |
| elevation | float | 观测俯仰角 | 度 | -90~90 | - | 10.0 | 水平为0度，向上为正 |
| detection_range | float | 探测距离 | 米 | 100~100000 | - | 20000 | 最大有效探测距离 |
| resolution | float | 角分辨率 | 毫弧度 | 0.01~10 | - | 0.06 | 最小可分辨角度 |
| field_of_view | float | 视场角 | 度 | 1~180 | - | 15.0 | 观测视场范围 |
| spectral_range | array | 光谱范围 | 米 | 1e-7~1e-4 | - | [3e-6, 14e-6] | 工作波长范围 |
| sensitivity | float | 灵敏度 | - | 0.1~1.0 | - | 0.95 | 探测灵敏度系数 |
| work_mode | string | 工作模式 | - | - | passive_search, active_tracking, coordinated_search | "coordinated_search" | 设备工作状态 |
| engine | float | 发动机温度 | 开尔文 | 300~800 | - | 520.0 | 红外目标发动机温度 |
| body | float | 机体温度 | 开尔文 | 250~400 | - | 305.0 | 红外目标机体温度 |
| background | float | 背景温度 | 开尔文 | 200~350 | - | 291.15 | 环境背景温度 |
| wavelength | float | 激光波长 | 米 | 1e-7~1e-5 | - | 1.064e-6 | 激光目标工作波长 |
| power | float | 激光功率 | 瓦特 | 1~10000 | - | 1800 | 激光目标输出功率 |

### 1.2 输出数据规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| device_id | integer | 设备编号 | - | 0~999 | - | 0 | 系统自动分配 |
| model | string | 设备型号 | - | - | - | "多目标_红外目标A" | 与输入一致 |
| device_type | string | 设备类型 | - | - | optical_target | "optical_target" | 固定值 |
| generation_timestamp | string | 数据生成时间 | - | - | - | "2025-08-15T22:52:02.277641" | ISO 8601格式 |
| sample_id | integer | 样本编号 | - | 0~4999 | - | 0 | 数据样本序号 |
| azimuth_deviation_mrad | float | 方位偏差 | 毫弧度 | 0~500 | - | 47.066 | 方位角测量偏差 |
| elevation_deviation_mrad | float | 俯仰偏差 | 毫弧度 | 0~500 | - | 77.284 | 俯仰角测量偏差 |
| total_deviation_mrad | float | 总偏差 | 毫弧度 | 0~700 | - | 90.488 | 综合角度偏差 |
| recognition_accuracy | float | 识别准确率 | - | 0~1 | - | 0.499 | 目标识别准确度 |
| distance_m | float | 目标距离 | 米 | 100~100000 | - | 15076.5 | 实际测量距离 |
| weather_factor | float | 天气影响因子 | - | 0.1~1.0 | - | 0.913 | 天气对性能的影响 |
| distance_factor | float | 距离影响因子 | - | 0.1~1.0 | - | 0.704 | 距离对性能的影响 |
| detection_range_m | float | 实际探测距离 | 米 | 100~150000 | - | 20110.9 | 当前条件下探测距离 |
| base_range_m | float | 基础探测距离 | 米 | 100~100000 | - | 20000 | 标准条件下探测距离 |
| target_contrast | float | 目标对比度 | - | 0.1~2.0 | - | 1.117 | 目标与背景对比度 |
| detection_probability | float | 探测概率 | - | 0~1 | - | 0.724 | 目标被探测到的概率 |
| base_probability | float | 基础探测概率 | - | 0~1 | - | 0.95 | 标准条件下探测概率 |
| noise_factor | float | 噪声影响因子 | - | 0.1~1.0 | - | 0.821 | 系统噪声对性能影响 |
| weather_condition | string | 天气条件 | - | - | clear_weather, haze, fog, rain | "clear_weather" | 当前天气状态 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:52:02.277641" | 数据记录时间 |

---

## 2. 光电干扰数据产生

光电干扰设备用于对敌方光电设备进行干扰，包括烟幕干扰、激光干扰、红外诱饵等。

### 2.1 输入数据规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| model | string | 干扰设备型号名称 | - | - | 自定义 | "多目标_烟幕干扰1" | 用于标识干扰设备类型 |
| latitude | float | 设备纬度坐标 | 度 | -90~90 | - | 39.9020 | WGS84坐标系 |
| longitude | float | 设备经度坐标 | 度 | -180~180 | - | 116.4020 | WGS84坐标系 |
| altitude | float | 设备海拔高度 | 米 | 0~50000 | - | 500.0 | 相对海平面高度 |
| azimuth | float | 干扰方位角 | 度 | 0~360 | - | 60.0 | 干扰指向方位 |
| elevation | float | 干扰俯仰角 | 度 | -90~90 | - | 0.0 | 干扰指向俯仰 |
| jamming_power | float | 干扰功率 | 瓦特 | 10~50000 | - | 1000 | 干扰设备输出功率 |
| jamming_frequency | float | 干扰频率 | 赫兹 | 1~10000 | - | 1200 | 干扰信号频率 |
| coverage_range | float | 覆盖距离 | 米 | 100~50000 | - | 4000 | 干扰有效作用距离 |
| beam_width | float | 波束宽度 | 度 | 0.1~180 | - | 2.0 | 激光干扰波束宽度 |
| duration | float | 持续时间 | 秒 | 10~3600 | - | 300 | 烟幕干扰持续时间 |
| coverage_radius | float | 覆盖半径 | 米 | 10~1000 | - | 150 | 烟幕覆盖半径 |
| wavelength | float | 干扰波长 | 米 | 1e-7~1e-5 | - | 0.532e-6 | 激光干扰波长 |
| work_mode | string | 工作模式 | - | - | continuous, pulse, area_denial, active_dazzling | "area_denial" | 干扰工作模式 |
| jamming_strategy | string | 干扰策略 | - | - | coordinated_obscuration, sensor_overload, laser_blinding | "coordinated_obscuration" | 干扰实施策略 |

### 2.2 输出数据规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| device_id | integer | 设备编号 | - | 0~999 | - | 0 | 系统自动分配 |
| model | string | 设备型号 | - | - | - | "多目标_烟幕干扰1" | 与输入一致 |
| device_type | string | 设备类型 | - | - | optical_jammer | "optical_jammer" | 固定值 |
| jammer_type | string | 干扰器类型 | - | - | smoke_screen, laser_dazzler, infrared_decoy | "smoke_screen" | 自动识别的干扰类型 |
| generation_timestamp | string | 数据生成时间 | - | - | - | "2025-08-15T22:51:40.857051" | ISO 8601格式 |
| sample_id | integer | 样本编号 | - | 0~4999 | - | 0 | 数据样本序号 |
| target_distance_m | float | 目标距离 | 米 | 100~50000 | - | 1467.6 | 被干扰目标距离 |
| base_effectiveness | float | 基础干扰效果 | - | 0~1 | - | 0.0 | 标准条件下干扰效果 |
| weather_factor | float | 天气影响因子 | - | 0.1~1.0 | - | 0.98 | 天气对干扰效果影响 |
| atmospheric_factor | float | 大气影响因子 | - | 0.1~1.0 | - | 0.996 | 大气传输对干扰影响 |
| target_vulnerability | float | 目标易损性 | - | 0.1~1.0 | - | 0.913 | 目标对干扰的敏感度 |
| final_effectiveness | float | 最终干扰效果 | - | 0~1 | - | 0.0 | 综合条件下干扰效果 |
| base_power_w | float | 基础功率 | 瓦特 | 10~50000 | - | 1000 | 设备额定功率 |
| mode_factor | float | 模式因子 | - | 0.1~1.0 | - | 0.686 | 工作模式对功率影响 |
| temp_factor | float | 温度因子 | - | 0.8~1.2 | - | 1.006 | 环境温度对功率影响 |
| efficiency | float | 工作效率 | - | 0.5~0.95 | - | 0.877 | 设备工作效率 |
| actual_power_w | float | 实际功率 | 瓦特 | 5~50000 | - | 690.5 | 实际消耗功率 |
| total_power_w | float | 总功率 | 瓦特 | 10~60000 | - | 787.8 | 包含损耗的总功率 |
| base_range_m | float | 基础覆盖距离 | 米 | 100~50000 | - | 4000 | 标准条件覆盖距离 |
| power_factor | float | 功率因子 | - | 0.5~2.0 | - | 1.0 | 功率对覆盖距离影响 |
| terrain_factor | float | 地形因子 | - | 0.5~1.5 | - | 1.061 | 地形对覆盖影响 |
| actual_range_m | float | 实际覆盖距离 | 米 | 50~75000 | - | 4213.8 | 当前条件覆盖距离 |
| coverage_angle_deg | float | 覆盖角度 | 度 | 5~360 | - | 161.1 | 干扰覆盖角度范围 |
| base_duration_s | float | 基础持续时间 | 秒 | 10~3600 | - | 300 | 标准条件持续时间 |
| wind_factor | float | 风力因子 | - | 0.3~1.2 | - | 0.5 | 风力对持续时间影响 |
| actual_duration_s | float | 实际持续时间 | 秒 | 5~4000 | - | 128.6 | 当前条件持续时间 |
| wind_speed_ms | float | 风速 | 米/秒 | 0~30 | - | 5.0 | 当前环境风速 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.857051" | 数据记录时间 |

---

## 3. 光电侦查数据产生

光电侦察设备用于发现、识别和跟踪目标，包括红外告警、激光告警、光电侦察等。

### 3.1 输入数据规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| model | string | 侦察设备型号名称 | - | - | 自定义 | "多目标_侦察系统1" | 用于标识侦察设备类型 |
| latitude | float | 设备纬度坐标 | 度 | -90~90 | - | 39.9200 | WGS84坐标系 |
| longitude | float | 设备经度坐标 | 度 | -180~180 | - | 116.4200 | WGS84坐标系 |
| altitude | float | 设备海拔高度 | 米 | 0~50000 | - | 2500.0 | 相对海平面高度 |
| detection_range | float | 探测距离 | 米 | 1000~200000 | - | 35000 | 最大探测距离 |
| resolution | float | 角分辨率 | 毫弧度 | 0.01~1.0 | - | 0.02 | 最小可分辨角度 |
| spectral_coverage | array | 光谱覆盖范围 | 米 | 1e-7~1e-4 | - | [3e-6, 12e-6] | 工作波长范围 |
| sensitivity | float | 探测灵敏度 | - | 0.5~0.99 | - | 0.96 | 探测灵敏度系数 |
| field_of_view | float | 视场角 | 度 | 5~180 | - | 30.0 | 观测视场范围 |
| multi_target_capacity | integer | 多目标处理能力 | 个 | 1~100 | - | 15 | 同时跟踪目标数量 |
| work_mode | string | 工作模式 | - | - | multi_target_tracking, coordinated_surveillance, passive_detection | "multi_target_tracking" | 侦察工作模式 |
| detection_mode | string | 探测模式 | - | - | infrared_warning, laser_warning, electro_optical | "infrared_warning" | 探测工作模式 |

### 3.2 输出数据规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| device_id | integer | 设备编号 | - | 0~999 | - | 0 | 系统自动分配 |
| model | string | 设备型号 | - | - | - | "多目标_侦察系统1" | 与输入一致 |
| device_type | string | 设备类型 | - | - | optical_recon | "optical_recon" | 固定值 |
| recon_type | string | 侦察器类型 | - | - | infrared_detector, laser_warning, electro_optical | "infrared_detector" | 自动识别的侦察类型 |
| detection_mode | string | 探测模式 | - | - | infrared_warning, laser_warning | "infrared_warning" | 探测工作模式 |
| work_mode | string | 工作模式 | - | - | multi_target_tracking, coordinated_surveillance | "multi_target_tracking" | 侦察工作模式 |
| generation_timestamp | string | 数据生成时间 | - | - | - | "2025-08-15T22:51:40.859550" | ISO 8601格式 |
| sample_id | integer | 样本编号 | - | 0~4999 | - | 0 | 数据样本序号 |
| target_present | boolean | 目标存在 | - | - | true, false | false | 是否检测到目标 |
| signal_strength | float | 信号强度 | - | 0~1 | - | 0.268 | 接收信号强度 |
| noise_level | float | 噪声水平 | - | 0~1 | - | 0.099 | 系统噪声水平 |
| snr_db | float | 信噪比 | 分贝 | -60~40 | - | 8.6 | 信号噪声比 |
| detected | boolean | 检测结果 | - | - | true, false | true | 是否成功检测 |
| result_type | string | 检测结果类型 | - | - | hit, miss, false_alarm, correct_rejection | "false_alarm" | 检测结果分类 |
| extracted_features | array | 提取特征 | - | - | spectral, spatial, temporal, polarization | ["spectral", "temporal"] | 成功提取的特征类型 |
| feature_quality | object | 特征质量 | - | 0~1 | - | {"spectral": 0.932, "temporal": 0.675} | 各特征提取质量 |
| overall_confidence | float | 整体置信度 | - | 0~1 | - | 0.804 | 特征提取整体置信度 |
| processing_time_s | float | 处理时间 | 秒 | 0.1~10 | - | 1.11 | 特征提取处理时间 |
| target_speed_ms | float | 目标速度 | 米/秒 | 0~500 | - | 17.2 | 跟踪目标运动速度 |
| target_direction_deg | float | 目标方向 | 度 | 0~360 | - | 202.9 | 目标运动方向 |
| tracking_accuracy | float | 跟踪精度 | - | 0.3~0.99 | - | 0.854 | 目标跟踪精度 |
| position_error_m | float | 位置误差 | 米 | 1~50 | - | 6.1 | 位置测量误差 |
| velocity_error_ms | float | 速度误差 | 米/秒 | 0.1~10 | - | 1.46 | 速度测量误差 |
| track_state | string | 跟踪状态 | - | - | acquiring, tracking, lost, coasting | "tracking" | 当前跟踪状态 |
| track_duration_s | float | 跟踪持续时间 | 秒 | 1~1000 | - | 131.6 | 连续跟踪时间 |
| speed_factor | float | 速度影响因子 | - | 0.5~1.0 | - | 0.993 | 目标速度对跟踪影响 |
| distance_factor | float | 距离影响因子 | - | 0.5~1.0 | - | 0.956 | 目标距离对跟踪影响 |
| true_type | string | 真实目标类型 | - | - | aircraft, missile, vehicle, ship, unknown | "unknown" | 目标真实类型 |
| recognized_type | string | 识别目标类型 | - | - | aircraft, missile, vehicle, ship, unknown | "missile" | 系统识别类型 |
| correct_recognition | boolean | 识别正确性 | - | - | true, false | false | 识别是否正确 |
| confidence | float | 识别置信度 | - | 0.1~0.99 | - | 0.65 | 识别结果置信度 |
| recognition_accuracy | float | 识别准确率 | - | 0.1~0.99 | - | 0.444 | 综合识别准确率 |
| distance_m | float | 目标距离 | 米 | 500~300000 | - | 18100.2 | 目标实际距离 |
| target_size_factor | float | 目标尺寸因子 | - | 0.3~2.0 | - | 0.634 | 目标尺寸对识别影响 |
| base_range_m | float | 基础探测距离 | 米 | 1000~200000 | - | 35000 | 标准条件探测距离 |
| weather_factor | float | 天气影响因子 | - | 0.3~1.0 | - | 0.922 | 天气对探测影响 |
| target_signature | float | 目标特征 | - | 0.3~2.0 | - | 1.046 | 目标信号特征强度 |
| sensor_performance | float | 传感器性能 | - | 0.5~1.1 | - | 0.943 | 传感器工作性能 |
| actual_range_m | float | 实际探测距离 | 米 | 500~250000 | - | 31856.2 | 当前条件探测距离 |
| base_probability | float | 基础发现概率 | - | 0.1~0.99 | - | 0.636 | 标准条件发现概率 |
| atmospheric_factor | float | 大气影响因子 | - | 0.5~1.0 | - | 0.913 | 大气条件对发现影响 |
| target_visibility | float | 目标可见性 | - | 0.3~1.5 | - | 0.855 | 目标可见性系数 |
| sensor_condition | float | 传感器状态 | - | 0.8~1.0 | - | 0.965 | 传感器工作状态 |
| discovery_probability | float | 发现概率 | - | 0.01~0.99 | - | 0.43 | 目标被发现概率 |
| weather_condition | string | 天气条件 | - | - | clear_weather, haze, fog, rain | "clear_weather" | 当前天气状态 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.859550" | 数据记录时间 |

---

## 通用配置参数

### 仿真配置 (simulation)

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| scenario_name | string | 仿真场景名称 | - | - | 自定义 | "多目标光电对抗场景" | 场景标识名称 |
| duration | float | 仿真时长 | 秒 | 1~86400 | - | 10.0 | 仿真运行时间 |
| time_step | float | 时间步长 | 秒 | 0.01~10 | - | 0.1 | 仿真计算步长 |
| data_count | integer | 数据生成数量 | 个 | 1~5000 | - | 3 | 每个设备生成数据条数 |
| output_types | array | 输出类型 | - | - | static_images, dynamic_images, parameters | ["static_images", "dynamic_images", "parameters"] | 输出数据类型 |
| weather_condition | string | 天气条件 | - | - | clear_weather, haze, fog, rain, snow | "clear_weather" | 环境天气状态 |
| temperature | float | 环境温度 | 开尔文 | 200~350 | - | 291.15 | 环境温度 |
| humidity | float | 相对湿度 | - | 0~1 | - | 0.6 | 空气相对湿度 |
| pressure | float | 大气压力 | 帕斯卡 | 80000~110000 | - | 101325 | 大气压力值 |
| wind_speed | float | 风速 | 米/秒 | 0~50 | - | 5.0 | 环境风速 |
| visibility | float | 能见度 | 米 | 100~50000 | - | 25000 | 大气能见度 |

### 系统配置 (system)

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| max_threads | integer | 最大线程数 | 个 | 1~32 | - | 8 | 并行处理线程数 |
| image_resolution | array | 图像分辨率 | 像素 | [320,240]~[4096,3072] | - | [640, 480] | 图像输出分辨率 |
| video_fps | integer | 视频帧率 | 帧/秒 | 1~60 | - | 30 | 视频输出帧率 |
| random_seed | integer | 随机种子 | - | 1~999999 | - | 1234 | 随机数生成种子，null为随机 |

---

## 输出文件结构

系统输出包含以下文件类型：

### 文件路径结构
```
simulation_results/
└── session_YYYYMMDD_HHMMSS_mmm/
    ├── images/          # 静态图像文件
    ├── videos/          # 动态视频文件
    ├── data/            # JSON数据文件
    ├── logs/            # 日志文件
    └── configs/         # 配置备份文件
```

### 输出数据类型

| 类型 | 说明 | 文件格式 | 示例文件名 |
|------|------|----------|------------|
| images | 静态图像文件列表 | PNG | target_0_static_0000.png |
| videos | 动态视频文件列表 | MP4 | target_0_dynamic.mp4 |
| data | JSON数据文件列表 | JSON | target_0_parameters.json |
| summary | 仿真摘要文件列表 | JSON | simulation_summary.json |

### 性能指标输出

| 字段 | 说明 | 单位 | 示例值 |
|------|------|------|--------|
| processing_speed | 数据处理速度 | 条/秒 | 0.14 |
| files_per_second | 文件生成速度 | 文件/秒 | 0.56 |
| thread_utilization | 线程利用率 | - | 0.8 |
| memory_usage | 内存使用情况 | 字节 | {"rss": 104857600, "vms": 209715200} |
| execution_efficiency | 执行效率统计 | - | {"data_per_second": 0.14, "total_operations": 3} |

---

## 使用示例

### 最小配置示例

```json
{
  "simulation": {
    "scenario_name": "基础测试场景",
    "duration": 10.0,
    "data_count": 1,
    "output_types": ["parameters"]
  },
  "system": {
    "max_threads": 4
  },
  "optical_targets": [
    {
      "model": "基础红外目标",
      "position": {
        "latitude": 39.9042,
        "longitude": 116.4074,
        "altitude": 1000.0
      },
      "observation_direction": {
        "azimuth": 0.0,
        "elevation": 0.0
      },
      "performance_params": {
        "detection_range": 5000
      },
      "work_mode": "passive_search"
    }
  ]
}
```

### 完整配置示例

参考 `PhoElec/config_templates/multi_target_config.json` 文件获取完整的配置示例。